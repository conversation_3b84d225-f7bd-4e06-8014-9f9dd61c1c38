/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    GSM.h
  * @brief   GSM模块AT指令控制头文件
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __GSM_H__
#define __GSM_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "usart.h"

/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* 服务器配置宏定义 */
#define GSM_SERVER_IP        "************"    // TCP服务器IP地址
#define GSM_SERVER_PORT      "48085"           // TCP服务器端口号

/* AT指令超时时间定义 */
#define GSM_AT_TIMEOUT_MS       1000           // AT指令超时时间(毫秒)
#define GSM_CONNECT_TIMEOUT_MS  1000           // 连接超时时间(毫秒)
#define GSM_POWER_ON_DELAY_MS   5000           // GSM电源开启后等待时间(毫秒)
#define GSM_AT_RETRY_COUNT   3                 // AT指令重试次数

/* 缓冲区大小定义 */
#define GSM_RX_BUFFER_SIZE    256              // 接收缓冲区大小

/* AT指令响应状态 */
typedef enum {
    GSM_OK = 0,           // 指令执行成功
    GSM_ERROR,            // 指令执行失败
    GSM_TIMEOUT,          // 指令超时
    GSM_BUSY              // 模块忙
} GSM_Status_t;

/* GSM模块状态 */
typedef enum {
    GSM_STATE_INIT = 0,   // 初始化状态
    GSM_STATE_READY,      // 就绪状态
    GSM_STATE_CONNECTED,  // 已连接状态
    GSM_STATE_ERROR       // 错误状态
} GSM_State_t;

/* GSM模块信息结构体 */
typedef struct {
    char model[32];       // 模块型号
    char ccid[32];        // CCID号
    uint16_t voltage;     // 电压值(mV)
    uint8_t signal;       // 信号强度(0-31)
    uint8_t network_reg;  // 网络注册状态
} GSM_Info_t;

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

/* 函数声明 */
// 初始化GSM模块
GSM_Status_t GSM_Init(void);

// 关闭回显
GSM_Status_t GSM_CloseEcho(void);

// 获取模块型号
GSM_Status_t GSM_GetModel(char* model);

// 获取CCID号
GSM_Status_t GSM_GetCCID(char* ccid);

// 获取模块电压
GSM_Status_t GSM_GetVoltage(uint16_t* voltage);

// 查询网络注册状态
GSM_Status_t GSM_CheckNetwork(uint8_t* reg_status);

// 获取信号强度
GSM_Status_t GSM_GetSignal(uint8_t* signal);

// 连接TCP服务器
GSM_Status_t GSM_ConnectServer(void);

// 关闭服务器连接
GSM_Status_t GSM_CloseServer(void);

// 设置非透传快发模式
GSM_Status_t GSM_SetQuickSend(void);

// 发送数据
GSM_Status_t GSM_SendData(const char* data, uint16_t length);

// 获取模块信息
GSM_Status_t GSM_GetInfo(GSM_Info_t* info);

// 获取当前状态
GSM_State_t GSM_GetState(void);

// 发送AT指令并等待响应（轮询方式）
GSM_Status_t GSM_SendATCommand(const char* command, const char* expected_response, uint32_t timeout);

// 带重试机制的AT指令发送
GSM_Status_t GSM_SendATCommandWithRetry(const char* command, const char* expected_response, uint32_t timeout, uint8_t retry_count);

// GSM电源控制
void GSM_PowerOn(void);
void GSM_PowerOff(void);

// GSM紧急关机指令
void GSM_EmergencyShutdown(void);

// GSM完整初始化流程（包含电源控制和等待）
GSM_Status_t GSM_FullInit(void);

// 启动网络指令监听
void GSM_StartNetworkCommandListener(void);

// 检查网络下发指令
void GSM_CheckNetworkCommand(void);

// 处理网络下发指令
void GSM_ProcessNetworkCommand(const char* command, uint16_t length);

/* USER CODE BEGIN Prototypes */

/* USER CODE END Prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __GSM_H__ */
